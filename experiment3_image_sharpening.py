"""
实验三：图像锐化处理程序
功能：使用给定的锐化算子对图像进行锐化处理，显示原图和锐化图
锐化算子：
[ 1  2  1]
[ 0  0  0]
[-1 -2 -1]
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

def create_test_image():
    """
    创建一个测试图像用于锐化处理演示
    Returns:
        test_image: 测试图像数组
    """
    # 创建一个300x300的图像
    size = 300
    test_image = np.zeros((size, size), dtype=np.uint8)
    
    # 添加一些几何图形和纹理
    # 1. 填充背景
    test_image.fill(100)
    
    # 2. 添加矩形
    cv2.rectangle(test_image, (50, 50), (150, 150), 200, -1)
    cv2.rectangle(test_image, (50, 50), (150, 150), 50, 3)
    
    # 3. 添加圆形
    cv2.circle(test_image, (220, 220), 40, 255, -1)
    cv2.circle(test_image, (220, 220), 40, 0, 2)
    
    # 4. 添加三角形
    triangle_points = np.array([[80, 200], [120, 250], [40, 250]], np.int32)
    cv2.fillPoly(test_image, [triangle_points], 180)
    cv2.polylines(test_image, [triangle_points], True, 30, 2)
    
    # 5. 添加文字效果（用线条模拟）
    cv2.line(test_image, (180, 80), (250, 80), 255, 3)
    cv2.line(test_image, (180, 100), (250, 100), 255, 3)
    cv2.line(test_image, (180, 120), (230, 120), 255, 3)
    
    # 6. 添加一些噪声以增加细节
    noise = np.random.normal(0, 10, (size, size))
    test_image = np.clip(test_image.astype(float) + noise, 0, 255).astype(np.uint8)
    
    return test_image

def apply_sharpening_filter(image, kernel):
    """
    应用锐化滤波器
    Args:
        image: 输入图像
        kernel: 锐化核
    Returns:
        sharpened_image: 锐化后的图像
    """
    # 使用OpenCV的filter2D函数应用卷积
    sharpened = cv2.filter2D(image, -1, kernel)
    
    # 确保像素值在有效范围内
    sharpened = np.clip(sharpened, 0, 255).astype(np.uint8)
    
    return sharpened

def manual_convolution(image, kernel):
    """
    手动实现卷积操作（用于教学演示）
    Args:
        image: 输入图像
        kernel: 卷积核
    Returns:
        result: 卷积结果
    """
    # 获取图像和核的尺寸
    img_height, img_width = image.shape
    kernel_height, kernel_width = kernel.shape
    
    # 计算填充大小
    pad_h = kernel_height // 2
    pad_w = kernel_width // 2
    
    # 对图像进行零填充
    padded_image = np.pad(image, ((pad_h, pad_h), (pad_w, pad_w)), mode='constant', constant_values=0)
    
    # 初始化结果图像
    result = np.zeros_like(image, dtype=np.float64)
    
    # 执行卷积操作
    for i in range(img_height):
        for j in range(img_width):
            # 提取当前窗口
            window = padded_image[i:i+kernel_height, j:j+kernel_width]
            # 计算卷积
            result[i, j] = np.sum(window * kernel)
    
    # 将结果限制在有效范围内
    result = np.clip(result, 0, 255).astype(np.uint8)
    
    return result

def analyze_sharpening_effect(original, sharpened):
    """
    分析锐化效果
    Args:
        original: 原始图像
        sharpened: 锐化后的图像
    """
    # 计算图像的梯度幅度（边缘强度）
    def calculate_gradient_magnitude(img):
        grad_x = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        return magnitude
    
    original_edges = calculate_gradient_magnitude(original)
    sharpened_edges = calculate_gradient_magnitude(sharpened)
    
    # 计算平均边缘强度
    original_edge_strength = np.mean(original_edges)
    sharpened_edge_strength = np.mean(sharpened_edges)
    
    # 计算对比度
    original_contrast = np.std(original)
    sharpened_contrast = np.std(sharpened)
    
    print(f"锐化效果分析：")
    print(f"原图平均边缘强度：{original_edge_strength:.2f}")
    print(f"锐化后平均边缘强度：{sharpened_edge_strength:.2f}")
    print(f"边缘增强倍数：{sharpened_edge_strength/original_edge_strength:.2f}")
    print(f"原图对比度：{original_contrast:.2f}")
    print(f"锐化后对比度：{sharpened_contrast:.2f}")
    print(f"对比度增强倍数：{sharpened_contrast/original_contrast:.2f}")

def display_sharpening_results(original, sharpened, kernel, save_path=None):
    """
    显示锐化处理结果
    Args:
        original: 原始图像
        sharpened: 锐化后的图像
        kernel: 使用的锐化核
        save_path: 保存路径
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 显示原始图像
    axes[0, 0].imshow(original, cmap='gray')
    axes[0, 0].set_title('原始图像', fontsize=12)
    axes[0, 0].axis('off')
    
    # 显示锐化后的图像
    axes[0, 1].imshow(sharpened, cmap='gray')
    axes[0, 1].set_title('锐化后图像', fontsize=12)
    axes[0, 1].axis('off')
    
    # 显示锐化核
    im = axes[1, 0].imshow(kernel, cmap='RdBu', vmin=-2, vmax=2)
    axes[1, 0].set_title('锐化算子', fontsize=12)
    
    # 在每个格子中显示数值
    for i in range(kernel.shape[0]):
        for j in range(kernel.shape[1]):
            axes[1, 0].text(j, i, f'{kernel[i, j]}', 
                           ha='center', va='center', fontsize=14, fontweight='bold')
    
    # 添加颜色条
    plt.colorbar(im, ax=axes[1, 0])
    
    # 显示差异图像
    difference = cv2.absdiff(sharpened, original)
    axes[1, 1].imshow(difference, cmap='hot')
    axes[1, 1].set_title('差异图像（锐化效果）', fontsize=12)
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"锐化处理结果已保存到：{save_path}")
        plt.close()
    else:
        plt.show()

def main():
    """
    主函数：演示图像锐化处理
    """
    print("=" * 60)
    print("实验三：图像锐化处理程序")
    print("=" * 60)
    
    # 定义锐化算子
    sharpening_kernel = np.array([
        [ 1,  2,  1],
        [ 0,  0,  0],
        [-1, -2, -1]
    ], dtype=np.float32)
    
    print("使用的锐化算子：")
    print(sharpening_kernel)
    
    # 1. 创建测试图像
    print("\n1. 创建测试图像...")
    test_image = create_test_image()
    
    # 保存原始测试图像
    cv2.imwrite("original_image.jpg", test_image)
    print("原始图像已保存为：original_image.jpg")
    
    # 2. 应用锐化滤波器
    print("\n2. 应用锐化滤波器...")
    sharpened_opencv = apply_sharpening_filter(test_image, sharpening_kernel)
    
    # 3. 使用手动卷积方法（用于对比）
    print("\n3. 使用手动卷积方法...")
    sharpened_manual = manual_convolution(test_image, sharpening_kernel)
    
    # 4. 保存锐化结果
    cv2.imwrite("sharpened_image_opencv.jpg", sharpened_opencv)
    cv2.imwrite("sharpened_image_manual.jpg", sharpened_manual)
    print("锐化图像已保存")
    
    # 5. 显示结果
    print("\n4. 显示锐化处理结果...")
    display_sharpening_results(test_image, sharpened_opencv, sharpening_kernel, 
                             "sharpening_results.png")
    
    # 6. 分析锐化效果
    print("\n5. 分析锐化效果...")
    analyze_sharpening_effect(test_image, sharpened_opencv)
    
    # 7. 比较不同方法的结果
    print("\n6. 比较OpenCV和手动卷积的结果...")
    difference = cv2.absdiff(sharpened_opencv, sharpened_manual)
    max_diff = np.max(difference)
    mean_diff = np.mean(difference)
    print(f"两种方法的最大差异：{max_diff}")
    print(f"两种方法的平均差异：{mean_diff:.6f}")
    
    print("\n实验三完成！")
    print("生成的文件：")
    print("- original_image.jpg (原始图像)")
    print("- sharpened_image_opencv.jpg (OpenCV锐化结果)")
    print("- sharpened_image_manual.jpg (手动卷积锐化结果)")
    print("- sharpening_results.png (锐化处理结果对比)")
    
    print("\n锐化算子原理说明：")
    print("1. 该算子是一个边缘检测算子，突出水平边缘")
    print("2. 正值权重在上方，负值权重在下方")
    print("3. 当遇到从暗到亮的水平边缘时，输出为正值（亮）")
    print("4. 当遇到从亮到暗的水平边缘时，输出为负值（暗）")
    print("5. 锐化效果通过增强边缘对比度来实现")

if __name__ == "__main__":
    main()
