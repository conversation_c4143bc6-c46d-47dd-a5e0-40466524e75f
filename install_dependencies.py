"""
安装图像处理实验所需的依赖库
"""

import subprocess
import sys

def install_package(package_name, pip_name=None):
    """
    安装Python包
    Args:
        package_name: 包的导入名称
        pip_name: pip安装时使用的名称（如果与导入名称不同）
    Returns:
        bool: 是否安装成功
    """
    if pip_name is None:
        pip_name = package_name
    
    print(f"正在安装 {pip_name}...")
    
    try:
        # 尝试导入，如果成功则已安装
        __import__(package_name)
        print(f"✓ {package_name} 已经安装")
        return True
    except ImportError:
        pass
    
    try:
        # 使用pip安装
        subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
        print(f"✓ {pip_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {pip_name} 安装失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 安装 {pip_name} 时出错: {e}")
        return False

def upgrade_pip():
    """
    升级pip到最新版本
    """
    print("正在升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip升级成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ pip升级失败: {e}")
        return False

def main():
    """
    主函数：安装所有必需的依赖
    """
    print("图像处理实验依赖安装程序")
    print("=" * 50)
    
    # 升级pip
    print("\n1. 升级pip...")
    upgrade_pip()
    
    # 需要安装的包列表 (导入名, pip安装名)
    packages = [
        ("numpy", "numpy"),
        ("cv2", "opencv-python"),
        ("matplotlib", "matplotlib"),
    ]
    
    print(f"\n2. 安装依赖库...")
    
    successful_installs = 0
    total_packages = len(packages)
    
    for import_name, pip_name in packages:
        if install_package(import_name, pip_name):
            successful_installs += 1
        print()  # 空行分隔
    
    # 安装总结
    print("=" * 50)
    print("安装总结")
    print("=" * 50)
    print(f"总包数: {total_packages}")
    print(f"成功安装: {successful_installs}")
    print(f"失败数量: {total_packages - successful_installs}")
    
    if successful_installs == total_packages:
        print("\n🎉 所有依赖都安装成功！")
        print("现在可以运行图像处理实验了。")
        print("\n运行方法:")
        print("1. 运行单个实验: python experiment1_image_io.py")
        print("2. 批量运行所有实验: python run_all_experiments.py")
    else:
        print("\n⚠️  部分依赖安装失败")
        print("请检查网络连接和Python环境")
        print("也可以尝试手动安装:")
        for import_name, pip_name in packages:
            print(f"pip install {pip_name}")
    
    # 验证安装
    print(f"\n3. 验证安装...")
    all_installed = True
    
    for import_name, pip_name in packages:
        try:
            __import__(import_name)
            print(f"✓ {import_name} 可以正常导入")
        except ImportError:
            print(f"✗ {import_name} 导入失败")
            all_installed = False
    
    if all_installed:
        print("\n✅ 所有库都可以正常使用！")
    else:
        print("\n❌ 部分库无法使用，请重新安装")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
    except Exception as e:
        print(f"\n安装程序出错: {e}")
    
    input("\n按Enter键退出...")
