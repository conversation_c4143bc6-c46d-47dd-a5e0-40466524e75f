# 图像处理实验报告

## 实验环境要求
- Python 3.x
- OpenCV (cv2): `pip install opencv-python`
- NumPy: `pip install numpy`
- Matplotlib: `pip install matplotlib`

## 实验一：图像读取、显示、存储程序

### 实验目的
编写程序实现图像的基本操作：读取图像文件、在窗口中显示图像、将图像保存到文件。

### 程序文件
`experiment1_image_io.py`

### 主要功能
1. **read_image()**: 读取图像文件，支持多种格式
2. **display_image()**: 显示图像，支持彩色和灰度图像
3. **save_image()**: 保存图像到指定路径
4. **create_sample_image()**: 创建示例图像用于演示

### 运行方法
```bash
python experiment1_image_io.py
```

### 预期输出
程序将创建以下文件：
- `sample_image.jpg`: 彩色示例图像
- `gray_image.jpg`: 灰度图像
- `display_sample_image.png`: 示例图像显示结果
- `display_loaded_image.png`: 读取图像显示结果
- `display_gray_image.png`: 灰度图像显示结果

### 程序运行结果说明
1. 程序首先创建一个包含渐变色彩、圆形和矩形的示例图像
2. 将示例图像保存为JPEG格式
3. 重新读取保存的图像，验证读取功能
4. 将彩色图像转换为灰度图像并保存
5. 所有显示结果都会保存为PNG格式的图片文件

---

## 实验二：图像傅里叶变换程序

### 实验目的
编写程序对图像进行傅里叶变换，显示原图、傅里叶变换图以及傅里叶变换中心化图。

### 程序文件
`experiment2_fourier_transform.py`

### 主要功能
1. **fourier_transform()**: 执行二维傅里叶变换
2. **display_fourier_results()**: 显示变换结果
3. **inverse_fourier_transform()**: 执行逆傅里叶变换
4. **analyze_frequency_components()**: 分析频率分量

### 运行方法
```bash
python experiment2_fourier_transform.py
```

### 预期输出
程序将创建以下文件：
- `test_image.jpg`: 测试图像
- `fourier_transform_results.png`: 傅里叶变换结果（包含原图、变换图、中心化图）
- `reconstructed_image.jpg`: 重构图像
- `reconstruction_comparison.png`: 原图与重构图对比

### 程序运行结果说明
1. **原始图像**: 包含矩形、圆形、线条等几何图形的测试图像
2. **傅里叶变换幅度谱**: 显示图像的频率分布，亮点表示该频率分量的强度
3. **中心化傅里叶变换**: 将低频分量移到中心，便于观察频率分布
4. **频率分量分析**: 输出低频和高频能量的占比
5. **重构验证**: 通过逆变换重构原图，验证变换的正确性

### 傅里叶变换原理
- 傅里叶变换将图像从空间域转换到频率域
- 低频分量对应图像的整体亮度和大致轮廓
- 高频分量对应图像的细节和边缘信息
- 中心化操作将低频分量移到频谱中心

---

## 实验三：图像锐化处理程序

### 实验目的
使用给定的锐化算子对图像进行锐化处理，显示原图和锐化图。

### 锐化算子
```
[ 1  2  1]
[ 0  0  0]
[-1 -2 -1]
```

### 程序文件
`experiment3_image_sharpening.py`

### 主要功能
1. **apply_sharpening_filter()**: 使用OpenCV应用锐化滤波器
2. **manual_convolution()**: 手动实现卷积操作
3. **analyze_sharpening_effect()**: 分析锐化效果
4. **display_sharpening_results()**: 显示处理结果

### 运行方法
```bash
python experiment3_image_sharpening.py
```

### 预期输出
程序将创建以下文件：
- `original_image.jpg`: 原始图像
- `sharpened_image_opencv.jpg`: OpenCV锐化结果
- `sharpened_image_manual.jpg`: 手动卷积锐化结果
- `sharpening_results.png`: 锐化处理结果对比（包含原图、锐化图、算子、差异图）

### 程序运行结果说明
1. **原始图像**: 包含矩形、圆形、三角形等几何图形的测试图像
2. **锐化后图像**: 经过锐化算子处理后的图像，边缘更加清晰
3. **锐化算子可视化**: 显示3x3锐化核的数值分布
4. **差异图像**: 显示锐化前后的差异，突出锐化效果
5. **效果分析**: 输出边缘强度和对比度的量化分析结果

### 锐化算子原理
- 该算子是一个边缘检测算子，主要突出水平边缘
- 正值权重在上方，负值权重在下方
- 遇到从暗到亮的水平边缘时输出正值（增亮）
- 遇到从亮到暗的水平边缘时输出负值（变暗）
- 通过增强边缘对比度实现锐化效果

---

## 运行所有实验

### 批量运行脚本
创建 `run_all_experiments.py` 来依次运行所有实验：

```python
import subprocess
import sys

experiments = [
    "experiment1_image_io.py",
    "experiment2_fourier_transform.py", 
    "experiment3_image_sharpening.py"
]

for exp in experiments:
    print(f"\n{'='*50}")
    print(f"运行 {exp}")
    print(f"{'='*50}")
    try:
        subprocess.run([sys.executable, exp], check=True)
        print(f"{exp} 运行完成")
    except subprocess.CalledProcessError as e:
        print(f"{exp} 运行失败: {e}")
```

### 注意事项
1. 确保安装了所有必需的Python库
2. 程序会在当前目录生成多个图像文件
3. 如果系统不支持图形界面，程序会自动保存结果为图片文件
4. 建议在有足够磁盘空间的目录中运行程序

---

## 实验总结

这三个实验涵盖了数字图像处理的基础内容：

1. **实验一**展示了图像的基本I/O操作，是图像处理的基础
2. **实验二**介绍了频域分析方法，帮助理解图像的频率特性
3. **实验三**演示了空域滤波技术，展示了卷积操作在图像增强中的应用

通过这些实验，可以深入理解数字图像处理的基本原理和实现方法。
