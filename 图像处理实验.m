%% 图像处理实验 - 读取、显示、存储

clear all;
close all;
clc;

fprintf('==============================================\n');
fprintf('图像处理实验 - 读取、显示、存储\n');
fprintf('==============================================\n');

%% 1. 读取指定的图像文件
图像路径 = '图片.jpg';  % 图像文件路径

try
    原图 = imread(图像路径);
    fprintf('✓ 成功读取图像: %s\n', 图像路径);
    fprintf('✓ 图像尺寸: %d x %d x %d\n', size(原图));
catch
    fprintf('✗ 无法读取图像文件: %s\n', 图像路径);
    return;
end

%% 2. 显示原始图像
fprintf('\n正在显示原始图像...\n');
figure('Name', '原始图像', 'NumberTitle', 'off');
imshow(原图);
title('原始图像', 'FontSize', 14);

%% 3. 保存图像副本
fprintf('正在保存图像副本...\n');
try
    imwrite(原图, '输出图像.jpg');
    fprintf('✓ 图像已保存为: 输出图像.jpg\n');
catch
    fprintf('✗ 保存图像失败\n');
end

%% 4. 转换为灰度图像
fprintf('正在转换为灰度图像...\n');
if size(原图, 3) == 3  % 如果是彩色图像
    灰度图 = rgb2gray(原图);
    fprintf('✓ 彩色图像已转换为灰度图像\n');
else
    灰度图 = 原图;  % 如果已经是灰度图像
    fprintf('✓ 图像已经是灰度图像\n');
end

%% 5. 显示灰度图像
fprintf('正在显示灰度图像...\n');
figure('Name', '灰度图像', 'NumberTitle', 'off');
imshow(灰度图);
title('灰度图像', 'FontSize', 14);

%% 6. 保存灰度图像
fprintf('正在保存灰度图像...\n');
try
    imwrite(灰度图, '灰度输出.jpg');
    fprintf('✓ 灰度图像已保存为: 灰度输出.jpg\n');
catch
    fprintf('✗ 保存灰度图像失败\n');
end

%% 7. 显示图像信息
fprintf('\n==============================================\n');
fprintf('图像信息\n');
fprintf('==============================================\n');
fprintf('原图尺寸: %d x %d\n', size(原图, 1), size(原图, 2));
if size(原图, 3) == 3
    fprintf('原图类型: 彩色图像 (RGB)\n');
else
    fprintf('原图类型: 灰度图像\n');
end
fprintf('灰度图尺寸: %d x %d\n', size(灰度图, 1), size(灰度图, 2));
fprintf('灰度图类型: 灰度图像\n');

%% 8. 完成提示
fprintf('\n==============================================\n');
fprintf('实验完成！\n');
fprintf('==============================================\n');
fprintf('生成的文件:\n');
fprintf('- 输出图像.jpg (原图副本)\n');
fprintf('- 灰度输出.jpg (灰度图像)\n');
fprintf('\n实验功能:\n');
fprintf('✓ 图像读取 - 使用imread()函数读取图像文件\n');
fprintf('✓ 图像显示 - 使用imshow()函数显示图像\n');
fprintf('✓ 图像存储 - 使用imwrite()函数保存图像\n');
fprintf('✓ 格式转换 - 使用rgb2gray()转换彩色到灰度\n');
