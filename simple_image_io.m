%% 简单的图像读取、显示、存储程序
% 最基本的图像处理操作演示

clear all;
close all;
clc;

fprintf('简单图像处理程序\n');
fprintf('==================\n');

%% 方法1：读取现有图像文件（如果有的话）
% 尝试读取常见的测试图像
try
    % MATLAB内置的示例图像
    img = imread('peppers.png');  % 或者使用 'cameraman.tif', 'coins.png'
    fprintf('成功读取内置示例图像\n');
catch
    % 如果没有内置图像，创建一个简单的测试图像
    fprintf('创建测试图像...\n');
    img = uint8(rand(200, 300, 3) * 255);  % 随机彩色图像
end

%% 显示原始图像
figure(1);
imshow(img);
title('原始图像');
fprintf('图像尺寸: %d x %d x %d\n', size(img));

%% 保存图像
imwrite(img, 'output_image.jpg');
fprintf('图像已保存为: output_image.jpg\n');

%% 重新读取保存的图像
loaded_img = imread('output_image.jpg');
fprintf('重新读取图像成功\n');

%% 显示读取的图像
figure(2);
imshow(loaded_img);
title('读取的图像');

%% 转换为灰度图像
if size(img, 3) == 3  % 如果是彩色图像
    gray_img = rgb2gray(img);
else
    gray_img = img;
end

%% 显示灰度图像
figure(3);
imshow(gray_img);
title('灰度图像');

%% 保存灰度图像
imwrite(gray_img, 'gray_output.jpg');
fprintf('灰度图像已保存为: gray_output.jpg\n');

fprintf('\n程序完成！\n');
fprintf('生成文件: output_image.jpg, gray_output.jpg\n');
