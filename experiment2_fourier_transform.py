"""
实验二：图像傅里叶变换程序
功能：对图像实现傅里叶变换，显示原图、傅里叶变换图以及傅里叶变换中心化图
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

def create_test_image():
    """
    创建一个测试图像用于傅里叶变换演示
    Returns:
        test_image: 测试图像数组
    """
    # 创建一个256x256的图像
    size = 256
    test_image = np.zeros((size, size), dtype=np.uint8)
    
    # 添加一些几何图形
    # 1. 矩形
    cv2.rectangle(test_image, (50, 50), (150, 100), 255, -1)
    
    # 2. 圆形
    cv2.circle(test_image, (200, 200), 30, 128, -1)
    
    # 3. 线条
    cv2.line(test_image, (0, 128), (255, 128), 200, 2)
    cv2.line(test_image, (128, 0), (128, 255), 200, 2)
    
    # 4. 添加一些噪声点
    for i in range(100):
        x = np.random.randint(0, size)
        y = np.random.randint(0, size)
        test_image[y, x] = np.random.randint(100, 255)
    
    return test_image

def fourier_transform(image):
    """
    对图像进行傅里叶变换
    Args:
        image: 输入图像（灰度图像）
    Returns:
        fft_result: 傅里叶变换结果
        fft_magnitude: 傅里叶变换幅度谱
        fft_shifted: 中心化的傅里叶变换结果
        fft_magnitude_shifted: 中心化的幅度谱
    """
    # 进行二维傅里叶变换
    fft_result = np.fft.fft2(image)
    
    # 计算幅度谱（取对数以便显示）
    fft_magnitude = np.log(np.abs(fft_result) + 1)
    
    # 进行频域中心化（将低频分量移到中心）
    fft_shifted = np.fft.fftshift(fft_result)
    
    # 计算中心化后的幅度谱
    fft_magnitude_shifted = np.log(np.abs(fft_shifted) + 1)
    
    return fft_result, fft_magnitude, fft_shifted, fft_magnitude_shifted

def display_fourier_results(original_image, fft_magnitude, fft_magnitude_shifted, save_path=None):
    """
    显示傅里叶变换结果
    Args:
        original_image: 原始图像
        fft_magnitude: 傅里叶变换幅度谱
        fft_magnitude_shifted: 中心化的幅度谱
        save_path: 保存路径
    """
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 显示原始图像
    axes[0].imshow(original_image, cmap='gray')
    axes[0].set_title('原始图像', fontsize=12)
    axes[0].axis('off')
    
    # 显示傅里叶变换幅度谱
    axes[1].imshow(fft_magnitude, cmap='gray')
    axes[1].set_title('傅里叶变换幅度谱', fontsize=12)
    axes[1].axis('off')
    
    # 显示中心化的傅里叶变换幅度谱
    axes[2].imshow(fft_magnitude_shifted, cmap='gray')
    axes[2].set_title('中心化傅里叶变换幅度谱', fontsize=12)
    axes[2].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"傅里叶变换结果已保存到：{save_path}")
        plt.close()
    else:
        plt.show()

def inverse_fourier_transform(fft_result):
    """
    进行逆傅里叶变换
    Args:
        fft_result: 傅里叶变换结果
    Returns:
        reconstructed_image: 重构的图像
    """
    # 进行逆傅里叶变换
    reconstructed = np.fft.ifft2(fft_result)
    
    # 取实部并转换为uint8格式
    reconstructed_image = np.real(reconstructed).astype(np.uint8)
    
    return reconstructed_image

def analyze_frequency_components(fft_shifted):
    """
    分析频率分量
    Args:
        fft_shifted: 中心化的傅里叶变换结果
    """
    height, width = fft_shifted.shape
    center_y, center_x = height // 2, width // 2
    
    # 计算不同频率区域的能量
    # 低频区域（中心区域）
    low_freq_region = fft_shifted[center_y-20:center_y+20, center_x-20:center_x+20]
    low_freq_energy = np.sum(np.abs(low_freq_region)**2)
    
    # 高频区域（边缘区域）
    high_freq_mask = np.ones_like(fft_shifted, dtype=bool)
    high_freq_mask[center_y-20:center_y+20, center_x-20:center_x+20] = False
    high_freq_energy = np.sum(np.abs(fft_shifted[high_freq_mask])**2)
    
    total_energy = low_freq_energy + high_freq_energy
    
    print(f"频率分量分析：")
    print(f"低频能量占比：{low_freq_energy/total_energy*100:.2f}%")
    print(f"高频能量占比：{high_freq_energy/total_energy*100:.2f}%")

def main():
    """
    主函数：演示图像傅里叶变换
    """
    print("=" * 60)
    print("实验二：图像傅里叶变换程序")
    print("=" * 60)
    
    # 1. 创建测试图像
    print("\n1. 创建测试图像...")
    test_image = create_test_image()
    
    # 保存原始测试图像
    cv2.imwrite("test_image.jpg", test_image)
    print("测试图像已保存为：test_image.jpg")
    
    # 2. 进行傅里叶变换
    print("\n2. 进行傅里叶变换...")
    fft_result, fft_magnitude, fft_shifted, fft_magnitude_shifted = fourier_transform(test_image)
    
    # 3. 显示结果
    print("\n3. 显示傅里叶变换结果...")
    display_fourier_results(test_image, fft_magnitude, fft_magnitude_shifted, 
                           "fourier_transform_results.png")
    
    # 4. 分析频率分量
    print("\n4. 分析频率分量...")
    analyze_frequency_components(fft_shifted)
    
    # 5. 进行逆变换验证
    print("\n5. 进行逆傅里叶变换验证...")
    reconstructed_image = inverse_fourier_transform(fft_result)
    
    # 计算重构误差
    reconstruction_error = np.mean(np.abs(test_image.astype(float) - reconstructed_image.astype(float)))
    print(f"重构误差：{reconstruction_error:.6f}")
    
    # 保存重构图像
    cv2.imwrite("reconstructed_image.jpg", reconstructed_image)
    print("重构图像已保存为：reconstructed_image.jpg")
    
    # 6. 显示对比结果
    print("\n6. 显示原图与重构图对比...")
    fig, axes = plt.subplots(1, 2, figsize=(10, 4))
    
    axes[0].imshow(test_image, cmap='gray')
    axes[0].set_title('原始图像', fontsize=12)
    axes[0].axis('off')
    
    axes[1].imshow(reconstructed_image, cmap='gray')
    axes[1].set_title(f'重构图像 (误差: {reconstruction_error:.6f})', fontsize=12)
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig("reconstruction_comparison.png", dpi=150, bbox_inches='tight')
    print("对比结果已保存为：reconstruction_comparison.png")
    plt.close()
    
    print("\n实验二完成！")
    print("生成的文件：")
    print("- test_image.jpg (测试图像)")
    print("- fourier_transform_results.png (傅里叶变换结果)")
    print("- reconstructed_image.jpg (重构图像)")
    print("- reconstruction_comparison.png (原图与重构图对比)")
    
    print("\n傅里叶变换原理说明：")
    print("1. 傅里叶变换将图像从空间域转换到频率域")
    print("2. 低频分量对应图像的整体亮度和大致轮廓")
    print("3. 高频分量对应图像的细节和边缘信息")
    print("4. 中心化操作将低频分量移到频谱中心，便于观察")

if __name__ == "__main__":
    main()
