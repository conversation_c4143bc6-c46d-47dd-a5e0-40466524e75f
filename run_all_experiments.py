"""
批量运行所有图像处理实验
"""

import subprocess
import sys
import os

def run_experiment(script_name):
    """
    运行单个实验脚本
    Args:
        script_name: 脚本文件名
    Returns:
        bool: 是否运行成功
    """
    print(f"\n{'='*60}")
    print(f"正在运行: {script_name}")
    print(f"{'='*60}")
    
    try:
        # 检查文件是否存在
        if not os.path.exists(script_name):
            print(f"错误: 文件 {script_name} 不存在")
            return False
        
        # 运行脚本
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              timeout=120)  # 2分钟超时
        
        # 输出结果
        if result.stdout:
            print("程序输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✓ {script_name} 运行成功")
            return True
        else:
            print(f"✗ {script_name} 运行失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ {script_name} 运行超时")
        return False
    except Exception as e:
        print(f"✗ {script_name} 运行出错: {e}")
        return False

def check_dependencies():
    """
    检查必要的依赖库
    Returns:
        bool: 依赖是否满足
    """
    print("检查依赖库...")
    
    required_packages = ['cv2', 'numpy', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖库: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        if 'cv2' in missing_packages:
            print("pip install opencv-python")
        if 'numpy' in missing_packages:
            print("pip install numpy")
        if 'matplotlib' in missing_packages:
            print("pip install matplotlib")
        return False
    
    return True

def main():
    """
    主函数：批量运行所有实验
    """
    print("图像处理实验批量运行程序")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装必要的依赖库后再运行")
        return
    
    # 实验脚本列表
    experiments = [
        ("实验一：图像读取、显示、存储", "experiment1_image_io.py"),
        ("实验二：图像傅里叶变换", "experiment2_fourier_transform.py"),
        ("实验三：图像锐化处理", "experiment3_image_sharpening.py")
    ]
    
    # 运行统计
    total_experiments = len(experiments)
    successful_experiments = 0
    
    # 逐个运行实验
    for exp_name, script_name in experiments:
        print(f"\n开始运行: {exp_name}")
        if run_experiment(script_name):
            successful_experiments += 1
        
        # 询问是否继续
        if successful_experiments < len(experiments):
            try:
                response = input(f"\n按Enter继续下一个实验，或输入'q'退出: ").strip().lower()
                if response == 'q':
                    break
            except KeyboardInterrupt:
                print("\n用户中断程序")
                break
    
    # 输出总结
    print(f"\n{'='*60}")
    print("实验运行总结")
    print(f"{'='*60}")
    print(f"总实验数: {total_experiments}")
    print(f"成功运行: {successful_experiments}")
    print(f"失败数量: {total_experiments - successful_experiments}")
    
    if successful_experiments == total_experiments:
        print("🎉 所有实验都运行成功！")
    else:
        print("⚠️  部分实验运行失败，请检查错误信息")
    
    # 列出生成的文件
    print(f"\n生成的文件:")
    generated_files = [
        "sample_image.jpg", "gray_image.jpg",
        "display_sample_image.png", "display_loaded_image.png", "display_gray_image.png",
        "test_image.jpg", "fourier_transform_results.png", 
        "reconstructed_image.jpg", "reconstruction_comparison.png",
        "original_image.jpg", "sharpened_image_opencv.jpg", 
        "sharpened_image_manual.jpg", "sharpening_results.png"
    ]
    
    existing_files = [f for f in generated_files if os.path.exists(f)]
    
    if existing_files:
        for file in existing_files:
            print(f"- {file}")
    else:
        print("没有找到生成的文件")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    
    input("\n按Enter键退出...")
