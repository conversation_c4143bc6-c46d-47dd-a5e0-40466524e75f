%% 图像读取显示存储程序

clear all;
close all;
clc;

%% 1. 读取指定的图像文件
图像路径 = '图片.jpg';  % 图像文件路径
原图 = imread(图像路径);
fprintf('成功读取图像: %s\n', 图像路径);
fprintf('图像尺寸: %d x %d x %d\n', size(原图));

%% 2. 显示原始图像
figure;
imshow(原图);
title('原始图像');

%% 3. 保存图像副本
imwrite(原图, '输出图像.jpg');
fprintf('图像已保存为: 输出图像.jpg\n');

%% 4. 转换为灰度图像
if size(原图, 3) == 3  % 如果是彩色图像
    灰度图 = rgb2gray(原图);
else
    灰度图 = 原图;  % 如果已经是灰度图像
end

%% 5. 显示灰度图像
figure;
imshow(灰度图);
title('灰度图像');

%% 6. 保存灰度图像
imwrite(灰度图, '灰度输出.jpg');
fprintf('灰度图像已保存为: 灰度输出.jpg\n');

fprintf('程序完成！\n');
