%% 图像读取、显示、存储程序 (MATLAB版本)
% 功能：读取图像、显示图像、存储图像

clear all;
close all;
clc;

fprintf('==============================================\n');
fprintf('图像读取、显示、存储程序 (MATLAB版本)\n');
fprintf('==============================================\n');

%% 1. 创建示例图像
fprintf('\n1. 创建示例图像...\n');

% 创建一个彩色示例图像
[rows, cols] = meshgrid(1:300, 1:400);
sample_image = zeros(300, 400, 3, 'uint8');

% 创建渐变效果
sample_image(:,:,1) = uint8(255 * rows / 300);      % 红色通道
sample_image(:,:,2) = uint8(255 * cols / 400);      % 绿色通道
sample_image(:,:,3) = uint8(255 * (rows + cols) / 700); % 蓝色通道

% 添加一些几何图形
% 添加白色圆形
[X, Y] = meshgrid(1:400, 1:300);
circle_mask = (X-200).^2 + (Y-150).^2 <= 50^2;
sample_image(repmat(circle_mask, [1, 1, 3])) = 255;

% 添加黑色矩形边框
sample_image(100:200, 100:300, :) = 0;
sample_image(105:195, 105:295, :) = sample_image(105:195, 105:295, :);

fprintf('示例图像创建完成\n');

%% 2. 显示示例图像
fprintf('\n2. 显示示例图像...\n');

figure('Name', '示例图像', 'NumberTitle', 'off');
imshow(sample_image);
title('创建的示例图像', 'FontSize', 14);

fprintf('示例图像显示完成\n');

%% 3. 保存示例图像
fprintf('\n3. 保存示例图像...\n');

% 保存为JPEG格式
imwrite(sample_image, 'sample_image_matlab.jpg', 'JPEG', 'Quality', 95);
fprintf('示例图像已保存为: sample_image_matlab.jpg\n');

% 保存为PNG格式
imwrite(sample_image, 'sample_image_matlab.png', 'PNG');
fprintf('示例图像已保存为: sample_image_matlab.png\n');

%% 4. 读取刚保存的图像
fprintf('\n4. 读取保存的图像...\n');

% 读取JPEG图像
loaded_image_jpg = imread('sample_image_matlab.jpg');
fprintf('成功读取JPEG图像，尺寸: %d x %d x %d\n', size(loaded_image_jpg));

% 读取PNG图像
loaded_image_png = imread('sample_image_matlab.png');
fprintf('成功读取PNG图像，尺寸: %d x %d x %d\n', size(loaded_image_png));

%% 5. 显示读取的图像
fprintf('\n5. 显示读取的图像...\n');

figure('Name', '读取的图像对比', 'NumberTitle', 'off');

% 显示原图
subplot(2, 2, 1);
imshow(sample_image);
title('原始图像', 'FontSize', 12);

% 显示读取的JPEG图像
subplot(2, 2, 2);
imshow(loaded_image_jpg);
title('读取的JPEG图像', 'FontSize', 12);

% 显示读取的PNG图像
subplot(2, 2, 3);
imshow(loaded_image_png);
title('读取的PNG图像', 'FontSize', 12);

% 显示差异图像（PNG vs 原图）
diff_image = abs(double(sample_image) - double(loaded_image_png));
subplot(2, 2, 4);
imshow(uint8(diff_image * 10)); % 放大10倍以便观察
title('差异图像 (放大10倍)', 'FontSize', 12);

fprintf('图像对比显示完成\n');

%% 6. 转换为灰度图像并保存
fprintf('\n6. 转换为灰度图像并保存...\n');

% 转换为灰度图像
gray_image = rgb2gray(loaded_image_jpg);
fprintf('图像已转换为灰度图像，尺寸: %d x %d\n', size(gray_image));

% 显示灰度图像
figure('Name', '灰度图像', 'NumberTitle', 'off');
imshow(gray_image);
title('灰度图像', 'FontSize', 14);
colormap(gray);

% 保存灰度图像
imwrite(gray_image, 'gray_image_matlab.jpg', 'JPEG', 'Quality', 95);
imwrite(gray_image, 'gray_image_matlab.png', 'PNG');
fprintf('灰度图像已保存为: gray_image_matlab.jpg 和 gray_image_matlab.png\n');

%% 7. 图像信息分析
fprintf('\n7. 图像信息分析...\n');

% 获取图像信息
img_info_jpg = imfinfo('sample_image_matlab.jpg');
img_info_png = imfinfo('sample_image_matlab.png');

fprintf('JPEG图像信息:\n');
fprintf('  文件名: %s\n', img_info_jpg.Filename);
fprintf('  格式: %s\n', img_info_jpg.Format);
fprintf('  尺寸: %d x %d\n', img_info_jpg.Width, img_info_jpg.Height);
fprintf('  颜色类型: %s\n', img_info_jpg.ColorType);
fprintf('  文件大小: %d 字节\n', img_info_jpg.FileSize);

fprintf('\nPNG图像信息:\n');
fprintf('  文件名: %s\n', img_info_png.Filename);
fprintf('  格式: %s\n', img_info_png.Format);
fprintf('  尺寸: %d x %d\n', img_info_png.Width, img_info_png.Height);
fprintf('  颜色类型: %s\n', img_info_png.ColorType);
fprintf('  文件大小: %d 字节\n', img_info_png.FileSize);

%% 8. 图像质量对比
fprintf('\n8. 图像质量对比...\n');

% 计算PSNR (峰值信噪比)
psnr_jpg = psnr(loaded_image_jpg, sample_image);
psnr_png = psnr(loaded_image_png, sample_image);

fprintf('图像质量对比 (PSNR):\n');
fprintf('  JPEG vs 原图: %.2f dB\n', psnr_jpg);
fprintf('  PNG vs 原图: %.2f dB\n', psnr_png);

% 计算SSIM (结构相似性指数)
ssim_jpg = ssim(loaded_image_jpg, sample_image);
ssim_png = ssim(loaded_image_png, sample_image);

fprintf('图像质量对比 (SSIM):\n');
fprintf('  JPEG vs 原图: %.4f\n', ssim_jpg);
fprintf('  PNG vs 原图: %.4f\n', ssim_png);

%% 9. 完成总结
fprintf('\n==============================================\n');
fprintf('程序运行完成！\n');
fprintf('==============================================\n');

fprintf('\n生成的文件:\n');
fprintf('- sample_image_matlab.jpg (JPEG格式示例图像)\n');
fprintf('- sample_image_matlab.png (PNG格式示例图像)\n');
fprintf('- gray_image_matlab.jpg (JPEG格式灰度图像)\n');
fprintf('- gray_image_matlab.png (PNG格式灰度图像)\n');

fprintf('\n程序功能演示:\n');
fprintf('✓ 图像创建 - 创建了包含渐变和几何图形的示例图像\n');
fprintf('✓ 图像显示 - 使用imshow()函数显示图像\n');
fprintf('✓ 图像保存 - 使用imwrite()保存为JPEG和PNG格式\n');
fprintf('✓ 图像读取 - 使用imread()读取保存的图像文件\n');
fprintf('✓ 格式转换 - 将彩色图像转换为灰度图像\n');
fprintf('✓ 质量分析 - 计算PSNR和SSIM评估图像质量\n');

fprintf('\n按任意键关闭所有图像窗口...\n');
pause;
close all;
