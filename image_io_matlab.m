%% 图像读取、显示、存储程序 (MATLAB简单版本)

clear all;
close all;
clc;

%% 1. 读取指定的图像文件
img_path = '图片.jpg';  % 图像文件路径
img = imread(img_path);
fprintf('成功读取图像: %s\n', img_path);
fprintf('图像尺寸: %d x %d x %d\n', size(img));

%% 2. 显示原始图像
figure;
imshow(img);
title('原始图像');

%% 3. 保存图像副本
imwrite(img, 'output_image.jpg');
fprintf('图像已保存为: output_image.jpg\n');

%% 4. 转换为灰度图像
if size(img, 3) == 3  % 如果是彩色图像
    gray_img = rgb2gray(img);
else
    gray_img = img;  % 如果已经是灰度图像
end

%% 5. 显示灰度图像
figure;
imshow(gray_img);
title('灰度图像');

%% 6. 保存灰度图像
imwrite(gray_img, 'gray_output.jpg');
fprintf('灰度图像已保存为: gray_output.jpg\n');

fprintf('程序完成！\n');
