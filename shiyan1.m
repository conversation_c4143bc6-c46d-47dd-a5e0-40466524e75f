%% 实验一：图像读取、显示、存储程序

clear all;
close all;
clc;

%% 1. 读取图像
图像路径 = '图片.jpg';
原图 = imread(图像路径);
fprintf('成功读取图像: %s\n', 图像路径);
fprintf('图像尺寸: %d x %d x %d\n', size(原图));

%% 2. 显示图像
figure;
imshow(原图);
title('原始图像');

%% 3. 存储图像
imwrite(原图, '输出图像.jpg');
fprintf('图像已保存为: 输出图像.jpg\n');

%% 4. 转换为灰度图像并存储
if size(原图, 3) == 3
    灰度图 = rgb2gray(原图);
else
    灰度图 = 原图;
end

figure;
imshow(灰度图);
title('灰度图像');

imwrite(灰度图, '灰度图像.jpg');
fprintf('灰度图像已保存为: 灰度图像.jpg\n');

fprintf('实验一完成！\n');
