"""
实验一：图像读取、显示、存储程序
功能：读取图像、显示图像、存储图像
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

def read_image(image_path):
    """
    读取图像
    Args:
        image_path: 图像文件路径
    Returns:
        image: 读取的图像数组
    """
    try:
        # 使用OpenCV读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误：无法读取图像文件 {image_path}")
            return None
        
        # OpenCV读取的图像是BGR格式，转换为RGB格式用于matplotlib显示
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        print(f"成功读取图像：{image_path}")
        print(f"图像尺寸：{image_rgb.shape}")
        return image_rgb
    except Exception as e:
        print(f"读取图像时发生错误：{e}")
        return None

def display_image(image, title="图像", save_path=None):
    """
    显示图像
    Args:
        image: 要显示的图像数组
        title: 图像标题
        save_path: 如果提供，将图像保存到此路径而不是显示
    """
    if image is None:
        print("错误：图像为空，无法显示")
        return

    plt.figure(figsize=(8, 6))
    if len(image.shape) == 3:  # 彩色图像
        plt.imshow(image)
    else:  # 灰度图像
        plt.imshow(image, cmap='gray')

    plt.title(title, fontsize=14)
    plt.axis('off')  # 不显示坐标轴
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"图像显示结果已保存到：{save_path}")
        plt.close()
    else:
        plt.show()

def save_image(image, output_path):
    """
    保存图像
    Args:
        image: 要保存的图像数组
        output_path: 输出文件路径
    """
    if image is None:
        print("错误：图像为空，无法保存")
        return False
    
    try:
        # 如果是RGB格式，需要转换为BGR格式用于OpenCV保存
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            success = cv2.imwrite(output_path, image_bgr)
        else:
            success = cv2.imwrite(output_path, image)
        
        if success:
            print(f"图像已成功保存到：{output_path}")
            return True
        else:
            print(f"保存图像失败：{output_path}")
            return False
    except Exception as e:
        print(f"保存图像时发生错误：{e}")
        return False

def create_sample_image():
    """
    创建一个示例图像用于演示
    Returns:
        sample_image: 示例图像数组
    """
    # 创建一个彩色渐变图像
    height, width = 300, 400
    sample_image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 创建渐变效果
    for i in range(height):
        for j in range(width):
            sample_image[i, j, 0] = int(255 * i / height)  # 红色通道
            sample_image[i, j, 1] = int(255 * j / width)   # 绿色通道
            sample_image[i, j, 2] = int(255 * (i + j) / (height + width))  # 蓝色通道
    
    # 添加一些几何图形
    cv2.circle(sample_image, (200, 150), 50, (255, 255, 255), -1)
    cv2.rectangle(sample_image, (100, 100), (300, 200), (0, 0, 0), 3)
    
    return sample_image

def main():
    """
    主函数：演示图像读取、显示、存储功能
    """
    print("=" * 50)
    print("实验一：图像读取、显示、存储程序")
    print("=" * 50)
    
    # 1. 创建示例图像
    print("\n1. 创建示例图像...")
    sample_image = create_sample_image()
    
    # 2. 显示示例图像
    print("\n2. 显示示例图像...")
    display_image(sample_image, "示例图像", "display_sample_image.png")

    # 3. 保存示例图像
    print("\n3. 保存示例图像...")
    sample_path = "sample_image.jpg"
    save_image(sample_image, sample_path)

    # 4. 读取刚保存的图像
    print("\n4. 读取保存的图像...")
    loaded_image = read_image(sample_path)

    # 5. 显示读取的图像
    if loaded_image is not None:
        print("\n5. 显示读取的图像...")
        display_image(loaded_image, "读取的图像", "display_loaded_image.png")

    # 6. 将图像转换为灰度图像并保存
    print("\n6. 转换为灰度图像并保存...")
    if loaded_image is not None:
        gray_image = cv2.cvtColor(loaded_image, cv2.COLOR_RGB2GRAY)
        display_image(gray_image, "灰度图像", "display_gray_image.png")
        save_image(gray_image, "gray_image.jpg")
    
    print("\n实验一完成！")
    print("生成的文件：")
    print("- sample_image.jpg (彩色示例图像)")
    print("- gray_image.jpg (灰度图像)")
    print("- display_sample_image.png (示例图像显示结果)")
    print("- display_loaded_image.png (读取图像显示结果)")
    print("- display_gray_image.png (灰度图像显示结果)")

if __name__ == "__main__":
    main()
